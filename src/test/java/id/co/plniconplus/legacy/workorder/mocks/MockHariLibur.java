package id.co.plniconplus.legacy.workorder.mocks;

import id.co.plniconplus.legacy.harilibur.*;
import io.quarkus.test.Mock;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/1/2025
 */
@Mock
@ApplicationScoped
public class MockH<PERSON>L<PERSON>ur implements HariLiburService {

    @Override
    public Uni<HariLiburResponseList> getListHariLibur(Empty request) {
        return createHariLiburResponseList();
    }

    @Override
    public Uni<HariLiburResponseList> getListHariLiburBetween(HariLiburBetweenRequest request) {
        return createHariLiburResponseList();
    }

    @Override
    public Uni<HariLiburResponseList> getListHariLiburByDate(HariLiburRequest request) {
        return createHariLiburResponseList();
    }

    private Uni<HariLiburResponseList> createHariLiburResponseList() {
        return Uni.createFrom().item(HariLiburResponseList.newBuilder()
                .addHariLiburList(HariLiburResponse.newBuilder()
                        .setTanggalLibur("2025-08-17")
                        .setKeteranganHariLibur("Kemerdekaan RI ke-80")
                        .build())
                .addHariLiburList(HariLiburResponse.newBuilder()
                        .setTanggalLibur("2025-08-18")
                        .setKeteranganHariLibur("Cuti Bersama Kemerdekaan RI ke-80")
                        .build())
                .build());
    }
}
