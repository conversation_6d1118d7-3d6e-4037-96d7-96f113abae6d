package id.co.plniconplus.legacy.workorder.mocks;

import id.co.plniconplus.legacy.dayafasa.DayaFasaResponseList;
import id.co.plniconplus.legacy.dayafasa.DayaFasaService;
import id.co.plniconplus.legacy.dayafasa.Empty;
import io.quarkus.test.Mock;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 9/1/2025
 */
@Mock
@ApplicationScoped
public class MockDayaFasa implements DayaFasaService {

    @Override
    public Uni<DayaFasaResponseList> getListDayaFasa(Empty request) {
        return Uni.createFrom().item(DayaFasaResponseList.newBuilder()
                .addDayaFasaList(id.co.plniconplus.legacy.dayafasa.DayaFasaResponse.newBuilder()
                        .setDaya(900)
                        .setFasa(1).build())
                .addDayaFasaList(id.co.plniconplus.legacy.dayafasa.DayaFasaResponse.newBuilder()
                        .setDaya(1300)
                        .setFasa(2).build())
                .build());
    }
}
