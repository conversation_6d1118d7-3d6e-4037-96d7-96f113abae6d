package id.co.plniconplus.legacy.materialentry;

import id.co.plniconplus.legacy.dayafasa.DayaFasaResponse;
import id.co.plniconplus.legacy.dayafasa.DayaFasaResponseList;
import id.co.plniconplus.legacy.dayafasa.Empty;
import id.co.plniconplus.legacy.dayafasa.MutinyDayaFasaServiceGrpc;
import id.co.plniconplus.legacy.harilibur.MutinyHariLiburServiceGrpc;
import id.co.plniconplus.legacy.wo.WorkOrder;
import id.co.plniconplus.legacy.wo.WorkOrderId;
import id.co.plniconplus.legacy.wo.WorkOrderRepo;
import id.co.plniconplus.legacy.wo.WorkOrderService;
import id.co.plniconplus.legacy.wo.info.pelanggan.InformasiPelanggan;
import id.co.plniconplus.legacy.wo.info.pelapor.InformasiPelapor;
import id.co.plniconplus.legacy.wo.info.petugas.InformasiPetugas;
import id.co.plniconplus.legacy.wo.material.kabel.MaterialKabel;
import id.co.plniconplus.legacy.wo.material.kvamaks.MaterialKvamaks;
import id.co.plniconplus.legacy.wo.material.kvarh.MaterialKvarh;
import id.co.plniconplus.legacy.wo.material.kwh.MaterialKwh;
import id.co.plniconplus.legacy.wo.material.modem.MaterialModem;
import id.co.plniconplus.legacy.wo.material.pembatas.MaterialPembatas;
import id.co.plniconplus.legacy.wo.material.saklarwaktu.MaterialSaklarWaktu;
import id.co.plniconplus.legacy.wo.material.trafo.arus.MaterialTrafoArus;
import id.co.plniconplus.legacy.wo.material.trafo.tegangan.MaterialTrafoTegangan;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.AssertSubscriber;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/8/2025
 */
@QuarkusTest
public class WorkOrderServiceTest {

    @Mock
    MutinyDayaFasaServiceGrpc.MutinyDayaFasaServiceStub dayaFasaService;

    @Mock
    MutinyHariLiburServiceGrpc.MutinyHariLiburServiceStub hariLiburService;

    @InjectMock
    WorkOrderRepo woRepo;

    @Inject
    WorkOrderService woService;

    private WorkOrder wo;

    private DayaFasaResponseList dayaFasaResponseList;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        {
            wo = new WorkOrder();
            WorkOrderId woId = new WorkOrderId();
            woId.noAgenda = "AG001";
            woId.unitUp = "1";
            wo.workOrderId = woId;
            wo.tanggalAgenda = LocalDateTime.of(2025, Month.AUGUST, 18, 8, 0, 0);
            wo.tanggalLunas = LocalDateTime.of(2025, Month.AUGUST, 19, 14, 0, 0);
            wo.tanggalIsiMaterial = LocalDateTime.of(2025, Month.AUGUST, 15, 9, 46, 0);
            wo.tanggalRemaja = LocalDateTime.of(2025, Month.AUGUST, 29, 9, 0, 0);
            wo.status = "01";
            wo.tarif = "1";
            wo.tarifLama = "1";
            wo.daya = "900";
            wo.dayaLama = "450";
            wo.jenisTransaksi = "PASANG BARU";
            wo.kodePaket = "1";
            wo.noWorkOrder = "1";
            wo.tanggalWorkOrder = LocalDateTime.of(2025, Month.AUGUST, 20, 14, 0, 0);
            wo.tanggalCatat = LocalDateTime.of(2025, Month.AUGUST, 16, 9, 46, 0);
            wo.userId = "1";
            wo.messagingStatus = 1;
            wo.hariSla = 1;
            wo.tanggalPerintahKerja = LocalDateTime.now();
            wo.tanggalPasangSaklarWaktu = LocalDateTime.now();
            InformasiPelanggan informasiPelanggan = new InformasiPelanggan();
            {
                informasiPelanggan.idPelanggan = "1";
                informasiPelanggan.namaPelanggan = "Nama Pelanggan";
                informasiPelanggan.alamatPelanggan = "Alamat Pelanggan";
                informasiPelanggan.noTelpPelanggan = "No Telp Pelanggan";
                informasiPelanggan.noHpPelanggan = "No Hp Pelanggan";
                wo.informasiPelanggan = informasiPelanggan;
            }
            InformasiPelapor informasiPelapor = new InformasiPelapor();
            {
                informasiPelapor.namaPemohon = "Nama Pemohon";
                informasiPelapor.alamatPemohon = "Alamat Pemohon";
                informasiPelapor.noTelpPemohon = "No Telp Pemohon";
                informasiPelapor.noHpPemohon = "No Hp Pemohon";
                wo.informasiPelapor = informasiPelapor;
            }
            InformasiPetugas informasiPetugas = new InformasiPetugas();
            {
                informasiPetugas.kodeVendor = "KODEVENDOR";
                informasiPetugas.noSuratPerintahKerja = "No Surat Perintah Kerja";
                informasiPetugas.noBeritaAcara = "No Berita Acara";
                wo.informasiPetugas = informasiPetugas;
            }
            MaterialKwh materialKwh = new MaterialKwh();
            {
                materialKwh.kodeKwh = "Kode Kwh";
                materialKwh.kodePembMeterKwh = "Kode001";
                materialKwh.merekKwh = "Merek Kwh";
                materialKwh.typeKwh = "001";
                materialKwh.noMeterKwh = "No Meter Kwh";
                materialKwh.noPabrikKwh = "001";
                materialKwh.noRegisterKwh = "No Register Kwh";
                materialKwh.tahunBuatKwh = "2000";
                materialKwh.tahunTeraKwh = "2000";
                materialKwh.jumlahKwh = 1;
                wo.materialKwh = materialKwh;
            }
            MaterialPembatas materialPembatas = new MaterialPembatas();
            {
                materialPembatas.kodeArusPembatas = "Kode Arus Pembatas";
                materialPembatas.kodePembPembatas = "Kode Pemb Pembatas";
                materialPembatas.merekPembatas = "Merek Pembatas";
                materialPembatas.jenisPembatas = "Jenis Pembatas";
                materialPembatas.ukuranSettingPembatas = "001";
                materialPembatas.fasaPembatas = "Fasa Pembatas";
                materialPembatas.tegangPembatas = "Tegang Pembatas";
                materialPembatas.typePembatas = "001";
                materialPembatas.noPembatas = "No Pembatas";
                materialPembatas.noPabrikPembatas = "001";
                materialPembatas.noRegistrasiPembatas = "No Registrasi Pembatas";
                materialPembatas.arusPembatas = 1L;
                materialPembatas.tahunBuatPembatas = "2000";
                materialPembatas.tahunTeraPembatas = "2000";
                materialPembatas.jumlahArusPembatas = 1;
                wo.materialPembatas = materialPembatas;
            }
            MaterialKabel materialKabel = new MaterialKabel();
            {
                materialKabel.kodeKabel = "Kode Kabel";
                materialKabel.kodePembKabel = "Kode001";
                materialKabel.merekKabel = "Merek Kabel";
                materialKabel.typeKabel = "001";
                materialKabel.noKabel = "No Kabel";
                materialKabel.noPabrikKabel = "001";
                materialKabel.noRegisterKabel = "No Register Kabel";
                materialKabel.tahunBuatKabel = "2000";
                materialKabel.tahunTeraKabel = "2000";
                materialKabel.jumlahKabel = 1;
                wo.materialKabel = materialKabel;
            }
            MaterialKvarh materialKvarh = new MaterialKvarh();
            {
                materialKvarh.kodeKvarh = "Kode Kvarh";
                materialKvarh.kodePembMeterKvarh = "Kode001";
                materialKvarh.merekKvarh = "Merek Kvarh";
                materialKvarh.typeKvarh = "001";
                materialKvarh.noMeterKvarh = "No Meter Kvarh";
                materialKvarh.noPabrikKvarh = "001";
                materialKvarh.noRegisterKvarh = "No Register Kvarh";
                materialKvarh.tahunBuatKvarh = "2000";
                materialKvarh.tahunTeraKvarh = "2000";
                materialKvarh.jumlahKvarh = 1;
                wo.materialKvarh = materialKvarh;
            }
            MaterialKvamaks materialKvamaks = new MaterialKvamaks();
            {
                materialKvamaks.kodeKvamaks = "Kode Kvamaks";
                materialKvamaks.kodePembMeterKvamaks = "Kode001";
                materialKvamaks.merekKvamaks = "Merek Kvamaks";
                materialKvamaks.typeKvamaks = "001";
                materialKvamaks.noMeterKvamaks = "No Meter Kvamaks";
                materialKvamaks.noPabrikKvmaks = "001";
                materialKvamaks.noRegisterKvamaks = "No Register Kvamaks";
                materialKvamaks.tahunBuatKvamaks = "2000";
                materialKvamaks.tahunTeraKvamaks = "2000";
                materialKvamaks.jumlahKvamaks = 1;
                wo.materialKvamaks = materialKvamaks;
            }
            MaterialSaklarWaktu materialSaklarWaktu = new MaterialSaklarWaktu();
            {
                materialSaklarWaktu.kodeSaklarWaktu = "Kode Saklar Waktu";
                materialSaklarWaktu.kodePembSaklarWaktu = "Kode001";
                materialSaklarWaktu.merekSaklarWaktu = "Merek Saklar Waktu";
                materialSaklarWaktu.typeSaklarWaktu = "001";
                materialSaklarWaktu.noSaklarWaktu = "No Saklar Waktu";
                materialSaklarWaktu.noPabrikSaklarWaktu = "001";
                materialSaklarWaktu.noRegisterSaklarWaktu = "1/29";
                materialSaklarWaktu.tahunBuatSaklarWaktu = "2000";
                materialSaklarWaktu.tahunTeraSaklarWaktu = "2000";
                materialSaklarWaktu.teganganSaklarWaktu = "154";
                materialSaklarWaktu.arusSaklarWaktu = "100";
                materialSaklarWaktu.ukuranSettingSaklarWaktu = "12";
                materialSaklarWaktu.fasaBatasSaklarWaktu = "1/1";
                materialSaklarWaktu.jumlahSaklarWaktu = 1;
                wo.materialSaklarWaktu = materialSaklarWaktu;
            }
            MaterialTrafoArus materialTrafoArus = new MaterialTrafoArus();
            {
                materialTrafoArus.kodeTrafoArus = "Kode Trafo Arus";
                materialTrafoArus.kodePembTrafoArus = "Kode001";
                materialTrafoArus.merekTrafoArus = "Merek Trafo Arus";
                materialTrafoArus.typeTrafoArus = "001";
                materialTrafoArus.noTrafoArus = "No Trafo Arus";
                materialTrafoArus.noPabrikTrafoArus = "001";
                materialTrafoArus.noRegisterTrafoArus = "1009";
                materialTrafoArus.tahunBuatTrafoArus = "2000";
                materialTrafoArus.tahunTeraTrafoArus = "2000";
                materialTrafoArus.jumlahTrafoArus = 1;
                wo.materialTrafoArus = materialTrafoArus;
            }
            MaterialTrafoTegangan materialTrafoTegangan = new MaterialTrafoTegangan();
            {
                materialTrafoTegangan.kodeTrafoTegangan = "Kode Trafo Tegangan";
                materialTrafoTegangan.kodePembTrafoTegangan = "Kode001";
                materialTrafoTegangan.merekTrafoTegangan = "Merek Trafo Tegangan";
                materialTrafoTegangan.typeTrafoTegangan = "001";
                materialTrafoTegangan.noTrafoTegangan = "No Trafo Tegangan";
                materialTrafoTegangan.noPabrikTrafoTegangan = "001";
                materialTrafoTegangan.noRegisterTrafoTegangan = "10034";
                materialTrafoTegangan.tahunBuatTrafoTegangan = "2000";
                materialTrafoTegangan.tahunTeraTrafoTegangan = "2000";
                materialTrafoTegangan.jumlahTrafoTegangan = 1;
                wo.materialTrafoTegangan = materialTrafoTegangan;
            }
            MaterialModem materialModem = new MaterialModem();
            {
                materialModem.kodeModem = "Kode Modem";
                materialModem.kodePembModem = "Kode001";
                materialModem.merekModem = "Merek Modem";
                materialModem.typeModem = "001";
                materialModem.noModem = "No Modem";
                materialModem.noPabrikModem = "001";
                materialModem.noRegisterModem = "No Register Modem";
                materialModem.tahunBuatModem = "2000";
                materialModem.tahunTeraModem = "2000";
                materialModem.operatorSeluler = "Operator Seluler";
                materialModem.speed = "Speed";
                materialModem.jumlahModem = 1;
                wo.materialModem = materialModem;
            }
        }
        {
            dayaFasaResponseList = DayaFasaResponseList.newBuilder()
                    .addAllDayaFasaList(List.of(
                            DayaFasaResponse.newBuilder()
                                    .setDaya(900)
                                    .setFasa(1).build(),
                            DayaFasaResponse.newBuilder()
                                    .setDaya(1300)
                                    .setFasa(2).build()
                    )).build();
        }
    }

    @Test
    void testGetIsianMaterial_EmptyWorkOrders() {
        PanacheQuery<WorkOrder> mockQuery = Mockito.mock(PanacheQuery.class);
        Mockito.when(woRepo.findAll(Mockito.any()))
                .thenReturn(mockQuery);
        Mockito.when(mockQuery.page(Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(mockQuery);
        Mockito.when(mockQuery.list())
                .thenReturn(Collections.emptyList());
        Mockito.when(dayaFasaService.getListDayaFasa(Mockito.any(Empty.class)))
                .thenReturn(Uni.createFrom().item(dayaFasaResponseList));
        Multi<MaterialEntryRecord> result = woService.getMaterialEntries(0, 20);
        AssertSubscriber<MaterialEntryRecord> subscriber = result.subscribe()
                .withSubscriber(AssertSubscriber.create(10));
        subscriber.awaitCompletion().assertCompleted().assertHasNotReceivedAnyItem();
    }
}
