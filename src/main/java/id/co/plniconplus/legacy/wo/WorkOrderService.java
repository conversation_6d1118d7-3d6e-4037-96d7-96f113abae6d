package id.co.plniconplus.legacy.wo;

import id.co.plniconplus.legacy.dayafasa.DayaFasaResponse;
import id.co.plniconplus.legacy.dayafasa.DayaFasaResponseList;
import id.co.plniconplus.legacy.dayafasa.Empty;
import id.co.plniconplus.legacy.dayafasa.MutinyDayaFasaServiceGrpc;
import id.co.plniconplus.legacy.harilibur.HariLiburBetweenRequest;
import id.co.plniconplus.legacy.harilibur.HariLiburResponse;
import id.co.plniconplus.legacy.harilibur.HariLiburResponseList;
import id.co.plniconplus.legacy.harilibur.MutinyHariLiburServiceGrpc;
import id.co.plniconplus.legacy.materialentry.MaterialEntryDto;
import io.quarkus.cache.CacheResult;
import io.quarkus.grpc.GrpcClient;
import io.quarkus.logging.Log;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.metrics.annotation.Counted;
import org.eclipse.microprofile.metrics.annotation.Timed;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@ApplicationScoped
public class WorkOrderService {

    @GrpcClient("daya-fasa")
    MutinyDayaFasaServiceGrpc.MutinyDayaFasaServiceStub dayaFasaService;

    @GrpcClient("hari-libur")
    MutinyHariLiburServiceGrpc.MutinyHariLiburServiceStub hariLiburService;

    @Inject
    WorkOrderRepo woRepo;

    @ConfigProperty(name = "grpc.timeout", defaultValue = "5")
    long grpcTimeout;

    @ConfigProperty(name = "daya.threshold", defaultValue = "22000")
    int dayaThreshold;

    @ConfigProperty(name = "default.fasa", defaultValue = "1")
    int defaultFasa;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;

    private final Map<String, Object> fallbacks = Map.of(
            "daya-fasa", Collections.emptyList(),
            "hari-libur-between", Collections.emptyList()
    );

    /**
     * Migrate from view FSO.VACTIVITY_GRID_ISIANMAT
     *
     * @return all isian material
     */
    public Multi<MaterialEntryDto> getMaterialEntries(int page, int size) {
        Sort sort = Sort.by("workOrderId.noAgenda").and("workOrderId.unitUp");

        return getDayaFasaMap().onItem().transformToMulti(dayaFasaMap ->
                Uni.createFrom().item(() -> woRepo.findAll(sort).page(page, size).list())
                        .emitOn(Infrastructure.getDefaultWorkerPool())
                        .onItem().transformToMulti(Multi.createFrom()::iterable)
                        .onItem().transformToUni(wo -> processWorkOrder(wo, dayaFasaMap))
                        .merge());
    }

    @CacheResult(cacheName = "daya-fasa-cache")
    public Uni<Map<Integer, Integer>> getDayaFasaMap() {
        return withGrpcRetry(dayaFasaService.getListDayaFasa(Empty.newBuilder().build())
                .map(DayaFasaResponseList::getDayaFasaListList), "daya-fasa")
                .map(list -> list.stream()
                        .collect(Collectors.toMap(DayaFasaResponse::getDaya, DayaFasaResponse::getFasa)));
    }

    @Timed(name = "work_order_processing_time", description = "Time spent processing work orders")
    @Counted(name = "work_order_processed", description = "Number of work orders processed")
    public Uni<MaterialEntryDto> processWorkOrder(WorkOrder wo, Map<Integer, Integer> dayaFasaMap) {
        if (wo == null)
            return Uni.createFrom().item(null);
        if (wo.jenisTransaksi == null || isNotSla(wo.jenisTransaksi))
            return Uni.createFrom().item(createBasicDto(wo, dayaFasaMap));
        return Uni.combine().all().unis(
                        getDurasiHariKerja(wo),
                        getSisaOrderHariSla(wo)
                ).asTuple()
                .onFailure().recoverWithItem(Tuple2.of(0, 0))
                .onFailure().invoke(failure ->
                        Log.warnv("Failed to process work order {0}/{1}: {2}",
                                wo.workOrderId.noAgenda, wo.workOrderId.unitUp, failure.getMessage()))
                .map(tuple -> dtoMapper(wo, dayaFasaMap, tuple));
    }

    private <T> Uni<T> withGrpcRetry(Uni<T> uni, String serviceName) {
        return uni
                .ifNoItem().after(Duration.ofSeconds(grpcTimeout)).fail()
                .onFailure().retry().withBackOff(Duration.ofSeconds(1), Duration.ofSeconds(5)).atMost(3)
                .onFailure().invoke(failure ->
                        Log.errorv("gRPC call to {0} failed: {1}", serviceName, failure.getMessage()))
                .onFailure().recoverWithItem(() -> {
                    Log.warnv("Using fallback for {0} service after retries failed", serviceName);
                    return getFallbackForService(serviceName);
                });
    }

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param jenisTransaksi Jenis transaksi pelanggan
     * @return <code>true</code> jika transaksi termasuk jenis yang memiliki SLA
     * <code>false</code> jika tidak
     */
    private boolean isNotSla(String jenisTransaksi) {
        return !JenisTransaksi.FLAG_SLA_ENABLED.contains(jenisTransaksi);
    }

    private MaterialEntryDto.MaterialEntryDtoBuilder dtoBuilder(WorkOrder wo) {
        return MaterialEntryDto.builder()
                .noAgenda(wo.workOrderId.noAgenda)
                .tanggalAgenda(wo.tanggalAgenda)
                .tanggalLunas(wo.tanggalLunas)
                .tanggalIsiMaterial(wo.tanggalIsiMaterial)
                .status(wo.status)
                .statusName(getStatus(wo.status))
                .unitUp(wo.workOrderId.unitUp)
                .idPelanggan(wo.informasiPelanggan.idPelanggan)
                .namaPelanggan(wo.informasiPelanggan.namaPelanggan)
                .alamatPelanggan(wo.informasiPelanggan.alamatPelanggan)
                .noTelpPelanggan(wo.informasiPelanggan.noTelpPelanggan)
                .noHpPelanggan(wo.informasiPelanggan.noHpPelanggan)
                .tarif(wo.tarif)
                .tarifLama(wo.tarifLama)
                .daya(wo.daya)
                .dayaLama(wo.dayaLama)
                .noSuratPerintahKerja(wo.informasiPetugas.noSuratPerintahKerja)
                .noBeritaAcara(wo.informasiPetugas.noBeritaAcara)
                .jenisTransaksi(wo.jenisTransaksi)
                .kodePaket(wo.kodePaket)
                .noWorkOrder(wo.noWorkOrder)
                .tanggalWorkOrder(wo.tanggalWorkOrder)
                .kodeVendor(wo.informasiPetugas.kodeVendor)
                .tanggalCatat(wo.tanggalCatat)
                .idUser(wo.userId)
                .noMeterKwh(wo.materialKwh.noMeterKwh)
                .kodePembMeterKwh(wo.materialKwh.kodePembMeterKwh)
                .merekKwh(wo.materialKwh.merekKwh)
                .noPabrikKwh(wo.materialKwh.noPabrikKwh)
                .typeKwh(wo.materialKwh.typeKwh)
                .tahunBuatKwh(wo.materialKwh.tahunBuatKwh)
                .tahunTeraKwh(wo.materialKwh.tahunTeraKwh)
                .merekPembatas(wo.materialPembatas.merekPembatas)
                .noPembatas(wo.materialPembatas.noPembatas)
                .typePembatas(wo.materialPembatas.typePembatas)
                .jenisPembatas(wo.materialPembatas.jenisPembatas)
                .ukuranSettingPembatas(wo.materialPembatas.ukuranSettingPembatas)
                .fasaPembatas(wo.materialPembatas.fasaPembatas)
                .tegangPembatas(wo.materialPembatas.tegangPembatas)
                .arusPembatas(wo.materialPembatas.arusPembatas)
                .tahunTeraPembatas(wo.materialPembatas.tahunTeraPembatas)
                .tahunBuatPembatas(wo.materialPembatas.tahunBuatPembatas)
                .kodeKabel(wo.materialKabel.kodeKabel)
                .merekKabel(wo.materialKabel.merekKabel)
                .typeKabel(wo.materialKabel.typeKabel)
                .jumlahKabel(wo.materialKabel.jumlahKabel)
                .noPabrikKabel(wo.materialKabel.noPabrikKabel)
                .noKabel(wo.materialKabel.noKabel)
                .kodePembMeterKvarh(wo.materialKvarh.kodePembMeterKvarh)
                .merekKvarh(wo.materialKvarh.merekKvarh)
                .noPabrikKvarh(wo.materialKvarh.noPabrikKvarh)
                .noMeterKvarh(wo.materialKvarh.noMeterKvarh)
                .typeKvarh(wo.materialKvarh.typeKvarh)
                .tahunTeraKvarh(wo.materialKvarh.tahunTeraKvarh)
                .tahunBuatKvarh(wo.materialKvarh.tahunBuatKvarh)
                .kodePembMeterKvamaks(wo.materialKvamaks.kodePembMeterKvamaks)
                .merekKvamaks(wo.materialKvamaks.merekKvamaks)
                .noPabrikKvamaks(wo.materialKvamaks.noPabrikKvmaks)
                .noRegisterKvamaks(wo.materialKvamaks.noRegisterKvamaks)
                .typeKvamaks(wo.materialKvamaks.typeKvamaks)
                .tahunTeraKvamaks(wo.materialKvamaks.tahunTeraKvamaks)
                .tahunBuatKvamaks(wo.materialKvamaks.tahunBuatKvamaks)
                .merekSalarWaktu(wo.materialSaklarWaktu.merekSaklarWaktu)
                .tanggalPasangSaklarWaktu(wo.tanggalPasangSaklarWaktu)
                .noSaklarWaktu(wo.materialSaklarWaktu.noSaklarWaktu)
                .typeSaklarWaktu(wo.materialSaklarWaktu.typeSaklarWaktu)
                .ukuranSettingSaklarWaktu(wo.materialSaklarWaktu.ukuranSettingSaklarWaktu)
                .fasaBatasSaklarWaktu(wo.materialSaklarWaktu.fasaBatasSaklarWaktu)
                .teganganSaklarWaktu(wo.materialSaklarWaktu.teganganSaklarWaktu)
                .arusSaklarWaktu(wo.materialSaklarWaktu.arusSaklarWaktu)
                .tahunTeraSaklarWaktu(wo.materialSaklarWaktu.tahunTeraSaklarWaktu)
                .tahunBuatSaklarWaktu(wo.materialSaklarWaktu.tahunBuatSaklarWaktu)
                .kodeTrafoArus(wo.materialTrafoArus.kodeTrafoArus)
                .noTrafoArus(wo.materialTrafoArus.noTrafoArus)
                .merekTrafoArus(wo.materialTrafoArus.merekTrafoArus)
                .typeTrafoArus(wo.materialTrafoArus.typeTrafoArus)
                .tahunTeraTrafoArus(wo.materialTrafoArus.tahunTeraTrafoArus)
                .tahunBuatTrafoArus(wo.materialTrafoArus.tahunBuatTrafoArus)
                .kodeTrafoTegangan(wo.materialTrafoTegangan.kodeTrafoTegangan)
                .noTrafoTegangan(wo.materialTrafoTegangan.noTrafoTegangan)
                .merekTrafoTegangan(wo.materialTrafoTegangan.merekTrafoTegangan)
                .typeTrafoTegangan(wo.materialTrafoTegangan.typeTrafoTegangan)
                .tahunTeraTrafoTegangan(wo.materialTrafoTegangan.tahunTeraTrafoTegangan)
                .tahunBuatTrafoTegangan(wo.materialTrafoTegangan.tahunBuatTrafoTegangan)
                .jumlahTrafoTegangan(wo.materialTrafoTegangan.jumlahTrafoTegangan.toString())
                .kodeModem(wo.materialModem.kodeModem)
                .noModem(wo.materialModem.noModem)
                .merekModem(wo.materialModem.merekModem)
                .typeModem(wo.materialModem.typeModem)
                .speed(wo.materialModem.speed)
                .operatorSeluler(wo.materialModem.operatorSeluler)
                .jumlahModem(wo.materialModem.jumlahModem)
                .tanggalPerintahKerja(wo.tanggalPerintahKerja)
                .messagingStatus(wo.messagingStatus);
    }

    private MaterialEntryDto createBasicDto(WorkOrder wo, Map<Integer, Integer> dayaFasaMap) {
        return dtoBuilder(wo)
                .hariSla("-")
                .durasiHariKerja("-")
                .sisaHariSla("-")
                .orderHariSla(0)
                .fasa(getFasa(wo.daya, dayaFasaMap))
                .fasaLama(getFasa(wo.dayaLama, dayaFasaMap)).build();
    }

    private Uni<Integer> getDurasiHariKerja(WorkOrder wo) {
        if (wo == null || wo.jenisTransaksi == null || isNotSla(wo.jenisTransaksi))
            return Uni.createFrom().item(0);

        return hitungDurasiHariKerja(wo.jenisTransaksi, wo.tanggalRemaja, wo.tanggalLunas, wo.tanggalRestitusi)
                .onItem().transform(duration -> Math.min(duration.intValue() - 1, wo.hariSla));
    }

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param wo Work order
     * @return Sisa order hari SLA
     */
    private Uni<Integer> getSisaOrderHariSla(WorkOrder wo) {
        if (wo == null || wo.jenisTransaksi == null || isNotSla(wo.jenisTransaksi))
            return Uni.createFrom().item(0);
        return hitungDurasiHariKerja(wo.jenisTransaksi, wo.tanggalRemaja, wo.tanggalLunas, wo.tanggalRestitusi)
                .onItem().transform(duration -> {
                    if (duration > wo.hariSla) {
                        return wo.hariSla - duration.intValue();
                    } else {
                        return wo.hariSla - duration.intValue() + 1;
                    }
                });
    }

    private MaterialEntryDto dtoMapper(WorkOrder wo, Map<Integer, Integer> dayaFasaMapper, Tuple2<Integer, Integer> tupleHariLibur) {
        return dtoBuilder(wo)
                .hariSla(getHariSla(wo))
                .durasiHariKerja(tupleHariLibur.getItem1() != 0 ? tupleHariLibur.getItem1().toString() : "-")
                .sisaHariSla(tupleHariLibur.getItem2() != 0 ? tupleHariLibur.getItem2().toString() : "-")
                .orderHariSla(tupleHariLibur.getItem2())
                .fasa(getFasa(wo.daya, dayaFasaMapper))
                .fasaLama(getFasa(wo.dayaLama, dayaFasaMapper))
                .build();
    }

    @SuppressWarnings("unchecked")
    private <T> T getFallbackForService(String serviceName) {
        return (T) fallbacks.getOrDefault(serviceName, null);
    }

    /**
     * Migrate from function FSO.GET_STATUS
     *
     * @param kdStatus Kode status W/O
     * @return Nama status W/O
     */
    private String getStatus(String kdStatus) {
        return switch (kdStatus) {
            case "00" -> "PK DARI AP2T";
            case "01" -> "ISIAN MATERIAL";
            case "02" -> "PENUGASAN W/O KE VENDOR";
            case "10" -> "PELAKSANAAN/RESPONSE W/O";
            case "20" -> "W/O DITERIMA PETUGAS";
            case "30" -> "PESAN MATERIAL";
            case "40" -> "MATERIAL DITERIMA";
            case "41" -> "RESET MATERIAL";
            case "50" -> "DALAM PERJALANAN";
            case "60" -> "SAMPAI-PASANG";
            case "61" -> "SAMPAI-TUNDA";
            case "70" -> "NYALA";
            case "71" -> "ISIAN DATA PEMASANGAN";
            case "72" -> "RETUR MATERIAL";
            case "73" -> "W/O SELESAI";
            case "75" -> "TUNDA-PENORMALAN-PESTA";
            case "76" -> "SAMPAI BONGKAR";
            case "77" -> "ISIAN DATA BONGKAR PESTA";
            case "79" -> "ENTRI SISA TOKEN PESTA";
            case "80" -> "Update AP2T";
            case "81" -> "PDL Manual Non FSO";
            case "88" -> "SAMPAI-PASANG";
            case "90" -> "PHOTO DIULANG";
            case "91" -> "PHOTO SUDAH DIULANG";
            case "98" -> "W/O DITOLAK (MIGRASI UNIT)";
            case "99" -> "W/O DITOLAK";
            default -> "";
        };
    }

    /**
     * Migrate from function FSO.f$_get_fasa
     *
     * @param daya           daya
     * @param dayaFasaMapper mapper grpc client
     * @return fasa
     */
    private Integer getFasa(String daya, Map<Integer, Integer> dayaFasaMapper) {
        if (daya == null)
            return defaultFasa;
        if (Integer.parseInt(daya) > dayaThreshold)
            return 3;
        return dayaFasaMapper.getOrDefault(Integer.valueOf(daya), defaultFasa);
    }

    /**
     * Migrate from function FSO.V_SLA_WO
     *
     * @param jenisTransaksi Jenis transaksi pelanggan
     * @param tglRemaja      Tanggal remaja
     * @param tglLunas       Tanggal lunas
     * @param tglRestitusi   Tanggal restitusi
     * @return Durasi hari kerja
     */
    private Uni<Long> hitungDurasiHariKerja(String jenisTransaksi,
                                            LocalDateTime tglRemaja,
                                            LocalDateTime tglLunas,
                                            LocalDateTime tglRestitusi) {
        if (isNotSla(jenisTransaksi))
            return Uni.createFrom().item(0L);
        LocalDateTime endDate = Objects.requireNonNullElseGet(tglRemaja,
                () -> Objects.requireNonNullElseGet(tglRestitusi,
                        () -> LocalDateTime.now().plusDays(1L)));
        return hitungDurasiHariKerja(tglLunas, endDate);
    }

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param wo Work order
     * @return Hari SLA
     */
    private String getHariSla(WorkOrder wo) {
        if (wo == null || wo.jenisTransaksi == null || isNotSla(wo.jenisTransaksi))
            return "-";
        return wo.hariSla.toString();
    }

    /**
     * Migrate from function FSO.f$HitungHariNew
     *
     * @param firstDate  Tanggal pertama
     * @param secondDate Tanggal kedua
     * @return Durasi hari kerja antara tanggal pertama dan kedua
     */
    private Uni<Long> hitungDurasiHariKerja(LocalDateTime firstDate, LocalDateTime secondDate) {
        if (firstDate == null || secondDate == null)
            return Uni.createFrom().item(0L);
        LocalDateTime tglAwal = firstDate.isBefore(secondDate) ? firstDate : secondDate;
        LocalDateTime tglAkhir = firstDate.isBefore(secondDate) ? secondDate : firstDate;
        return getHariLiburCache(tglAwal, tglAkhir)
                .map(holidayMap -> calculateWorkingDays(tglAwal, tglAkhir, holidayMap));
    }

    @CacheResult(cacheName = "hari-libur-cache")
    public Uni<Map<String, List<HariLiburResponse>>> getHariLiburCache(LocalDateTime start, LocalDateTime end) {
        HariLiburBetweenRequest request = HariLiburBetweenRequest.newBuilder()
                .setStartDate(start.format(DATE_FORMATTER))
                .setEndDate(end.format(DATE_FORMATTER)).build();
        return withGrpcRetry(hariLiburService.getListHariLiburBetween(request)
                .map(HariLiburResponseList::getHariLiburListList), "hari-libur-between")
                .map(list -> Map.of("holidays", list));
    }

    private long calculateWorkingDays(LocalDateTime start, LocalDateTime end,
                                      Map<String, List<HariLiburResponse>> holidayMap) {
        List<HariLiburResponse> holidays = holidayMap.get("holidays");
        long totalDays = ChronoUnit.DAYS.between(start, end);
        long weekendDays = calculateWeekendDays(start, end);
        long holidayDays = calculateHolidayDays(start, end, holidays);
        return Math.max(totalDays - weekendDays - holidayDays, 0L);
    }

    private long calculateWeekendDays(LocalDateTime start, LocalDateTime end) {
        long days = ChronoUnit.DAYS.between(start, end);
        long weekends = 0;
        for (int i = 0; i <= days; i++) {
            LocalDateTime currentDate = start.plusDays(i);
            DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
            if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
                weekends++;
            }
        }
        return weekends;
    }

    private long calculateHolidayDays(LocalDateTime start, LocalDateTime end, List<HariLiburResponse> holidays) {
        if (holidays == null || holidays.isEmpty())
            return 0;
        return holidays.stream()
                .filter(holiday -> {
                    try {
                        LocalDate holidayDate = LocalDate.parse(holiday.getTanggalLibur(), DATE_FORMATTER);
                        LocalDateTime holidayDateTime = holidayDate.atStartOfDay();
                        return !holidayDateTime.isBefore(start) && !holidayDateTime.isAfter(end) &&
                                holidayDateTime.getDayOfWeek() != DayOfWeek.SATURDAY &&
                                holidayDateTime.getDayOfWeek() != DayOfWeek.SUNDAY;
                    } catch (DateTimeParseException e) {
                        Log.warnv("Invalid holiday date format: {0}", e.getMessage());
                        return false;
                    }
                })
                .count();
    }
}
