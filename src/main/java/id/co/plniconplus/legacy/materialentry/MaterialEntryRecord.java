package id.co.plniconplus.legacy.materialentry;

import io.quarkus.runtime.annotations.RegisterForReflection;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/7/2025
 */
@RegisterForReflection
public record MaterialEntryRecord(
        String noAgenda,
        LocalDateTime tanggalAgenda,
        LocalDateTime tanggalLunas,
        LocalDateTime tanggalIsiMaterial,
        String status,
        String statusName,
        String unitUp,
        String namaUp,
        String unitAp,
        String namaAp,
        String unitUpi,
        String namaUpi,
        String idPelanggan,
        String namaPelanggan,
        String alamatPelanggan,
        String noTelpPelanggan,
        String noHpPelanggan,
        String tarif,
        String tarifLama,
        String daya,
        String dayaLama,
        String noSuratPerintahKerja,
        String noBeritaAcara,
        String jenisTransaksi,
        String kodePaket,
        String namaPaket,
        String noWorkOrder,
        LocalDateTime tanggalWorkOrder,
        String kodeVendor,
        String namaVendor,
        LocalDateTime tanggalCatat,
        String idUser,
        String namaUser,
        String noMeterKwh,
        String kodePembMeterKwh,
        String merekKwh,
        String noPabrikKwh,
        String typeKwh,
        String tarifIndex,
        String tahunBuatKwh,
        String tahunTeraKwh,
        String tinggiMeterKwh,
        String trafoArusPrimer1,
        String trafoArusPrimer2,
        String trafoArusSekunder1,
        String trafoArusSekunder2,
        String trafoTeganganPrimer,
        String trafoTeganganSekunder,
        String merekPembatas,
        String noPembatas,
        String typePembatas,
        String jenisPembatas,
        String ukuranSettingPembatas,
        String fasaPembatas,
        String tegangPembatas,
        Long arusPembatas,
        String tahunTeraPembatas,
        String tahunBuatPembatas,
        String kodeKabel,
        String merekKabel,
        String jenisKabel,
        String typeKabel,
        String penampangKabel,
        Integer jumlahKabel,
        String jumlahCcoa,
        String nyyKabel,
        String nyaKabel,
        String jumlahNyyKabel,
        String jumlahNyaKabel,
        String oka,
        String clamp,
        String noSegelOk1,
        String noSegelOk2,
        String noSegelKwh1,
        String noSegelKwh2,
        String noSegelMcb1,
        String noPabrikKabel,
        String noKabel,
        String kodePembMeterKvarh,
        String merekKvarh,
        String noPabrikKvarh,
        String noMeterKvarh,
        String typeKvarh,
        String tahunTeraKvarh,
        String tahunBuatKvarh,
        String merekTrafoArusKvarh,
        String typeTrafoArusKvarh,
        String tahunBuatTrafoArusKvarh,
        String tahunTeraTrafoArusKvarh,
        String trafoArusKvarhPrimer1,
        String trafoArusKvarhPrimer2,
        String trafoArusKvarhSekunder1,
        String trafoArusKvarhSekunder2,
        String merekTrafoTeganganKavarh,
        String typeTrafoTeganganKvarh,
        String trafoTeganganKvarhPrimer,
        String trafoTeganganKvarhSekunder,
        String tahunBuatTrafoTeganganKvarh,
        String tahunTeraTrafoTeganganKvarh,
        String kodePembMeterKvamaks,
        String merekKvamaks,
        String noPabrikKvamaks,
        String noRegisterKvamaks,
        String typeKvamaks,
        String tahunTeraKvamaks,
        String tahunBuatKvamaks,
        String merekTrafoArusKvamaks,
        String typeTrafoArusKvamaks,
        String tahunBuatTrafoArusKvamaks,
        String tahunTeraTrafoArusKvamaks,
        String trafoArusKvamaksPrimer1,
        String trafoArusKvamaksPrimer2,
        String trafoArusKvamaksSekunder1,
        String trafoArusKvamaksSekunder2,
        String merekTrafoTeganganKvamaks,
        String typeTrafoTeganganKvamaks,
        String trafoTeganganKvamaksPrimer,
        String trafoTeganganKvamaksSekunder,
        String tahunBuatTrafoTeganganKvamaks,
        String tahunTeraTrafoTeganganKvamaks,
        String merekSalarWaktu,
        LocalDateTime tanggalPasangSaklarWaktu,
        String noSaklarWaktu,
        String typeSaklarWaktu,
        String jenisSaklarWaktu,
        String ukuranSettingSaklarWaktu,
        String fasaBatasSaklarWaktu,
        String teganganSaklarWaktu,
        String arusSaklarWaktu,
        String tahunTeraSaklarWaktu,
        String tahunBuatSaklarWaktu,
        String kodeTrafoArus,
        String noTrafoArus,
        String merekTrafoArus,
        String typeTrafoArus,
        String tahunTeraTrafoArus,
        String tahunBuatTrafoArus,
        String kodeTrafoTegangan,
        String noTrafoTegangan,
        String merekTrafoTegangan,
        String typeTrafoTegangan,
        String tahunTeraTrafoTegangan,
        String tahunBuatTrafoTegangan,
        String jumlahTrafoTegangan,
        String kodeModem,
        String noModem,
        String merekModem,
        String typeModem,
        String speed,
        String operatorSeluler,
        String simCard,
        Integer jumlahModem,
        LocalDateTime tanggalPerintahKerja,
        Integer messagingStatus,
        String hariSla,
        String durasiHariKerja,
        String sisaHariSla,
        Integer orderHariSla,
        Integer fasa,
        Integer fasaLama) {
}
